// User model definitions for ReWear platform

// User roles
export const USER_ROLES = {
  USER: 'user',
  ADMIN: 'admin'
}

// User schema structure
export const createUser = ({
  email,
  password,
  name,
  role = USER_ROLES.USER,
  profileImage = null,
  phone = null,
  address = null,
  points = 100, // Welcome bonus
  totalPointsEarned = 100,
  totalPointsSpent = 0,
  swapCount = 0,
  createdAt = new Date(),
  updatedAt = new Date(),
  isActive = true
}) => ({
  email,
  password,
  name,
  role,
  profileImage,
  phone,
  address,
  points,
  totalPointsEarned,
  totalPointsSpent,
  swapCount,
  createdAt,
  updatedAt,
  isActive
})

// User session structure
export const createUserSession = ({
  id,
  email,
  name,
  role,
  points = 0,
  profileImage = null
}) => ({
  id,
  email,
  name,
  role,
  points,
  profileImage
})
