// Item model definitions for ReWear platform

// Item status types
export const ITEM_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  SWAPPED: 'swapped',
  AVAILABLE: 'available'
}

// Item condition types
export const ITEM_CONDITION = {
  NEW: 'new',
  LIKE_NEW: 'like-new',
  GOOD: 'good',
  FAIR: 'fair',
  POOR: 'poor'
}

// Item category types
export const ITEM_CATEGORY = {
  TOPS: 'tops',
  BOTTOMS: 'bottoms',
  DRESSES: 'dresses',
  OUTERWEAR: 'outerwear',
  SHOES: 'shoes',
  ACCESSORIES: 'accessories',
  OTHER: 'other'
}

// Swap types
export const SWAP_TYPE = {
  DIRECT: 'direct',
  POINTS: 'points'
}

// Swap request status
export const SWAP_REQUEST_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// Create item structure
export const createItem = ({
  title,
  description,
  category,
  condition,
  size,
  brand = null,
  originalPrice = null,
  pointsValue,
  images = [],
  userId,
  status = ITEM_STATUS.PENDING,
  rejectionReason = null,
  createdAt = new Date(),
  updatedAt = new Date(),
  swappedAt = null,
  swappedWithUserId = null,
  swappedWithItemId = null,
  swapType = null,
  tags = [],
  isActive = true
}) => ({
  title,
  description,
  category,
  condition,
  size,
  brand,
  originalPrice,
  pointsValue,
  images,
  userId,
  status,
  rejectionReason,
  createdAt,
  updatedAt,
  swappedAt,
  swappedWithUserId,
  swappedWithItemId,
  swapType,
  tags,
  isActive
})

// Create swap request structure
export const createSwapRequest = ({
  requesterId,
  requesterItemId = null,
  targetItemId,
  targetUserId,
  swapType,
  pointsOffered = null,
  message = null,
  status = SWAP_REQUEST_STATUS.PENDING,
  createdAt = new Date(),
  updatedAt = new Date(),
  completedAt = null
}) => ({
  requesterId,
  requesterItemId,
  targetItemId,
  targetUserId,
  swapType,
  pointsOffered,
  message,
  status,
  createdAt,
  updatedAt,
  completedAt
})

// Helper functions
export const getStatusColor = (status) => {
  switch (status) {
    case ITEM_STATUS.PENDING:
      return 'bg-yellow-100 text-yellow-800'
    case ITEM_STATUS.APPROVED:
      return 'bg-green-100 text-green-800'
    case ITEM_STATUS.REJECTED:
      return 'bg-red-100 text-red-800'
    case ITEM_STATUS.SWAPPED:
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export const getConditionColor = (condition) => {
  switch (condition) {
    case ITEM_CONDITION.NEW:
      return 'bg-green-100 text-green-800'
    case ITEM_CONDITION.LIKE_NEW:
      return 'bg-blue-100 text-blue-800'
    case ITEM_CONDITION.GOOD:
      return 'bg-yellow-100 text-yellow-800'
    case ITEM_CONDITION.FAIR:
      return 'bg-orange-100 text-orange-800'
    case ITEM_CONDITION.POOR:
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
