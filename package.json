{"name": "rewear-community-exchange", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js", "test-setup": "node scripts/test-setup.js", "reset-admin-password": "node scripts/reset-admin-password.js", "fix-admin": "node scripts/fix-admin-user.js", "add-test-items": "node scripts/add-test-items.js", "fix-default-items": "node scripts/fix-default-items.js"}, "dependencies": {"@hookform/resolvers": "^3.7.0", "autoprefixer": "^10.4.21", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.408.0", "mongodb": "^6.8.0", "mongoose": "^8.5.1", "multer": "^1.4.5-lts.1", "next": "14.2.5", "next-auth": "^4.24.7", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.1", "sharp": "^0.33.4", "tailwind-merge": "^2.4.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.11", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}