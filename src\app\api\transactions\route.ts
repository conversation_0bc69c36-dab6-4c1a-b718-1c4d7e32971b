import { NextRequest, NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'
import { getUserFromToken } from '@/lib/auth'
import { Transaction, TransactionWithDetails, CreateTransactionInput } from '@/models/Transaction'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'earned' | 'spent' | 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit
    
    const db = await getDatabase()
    const transactionsCollection = db.collection<Transaction>('transactions')
    
    // Build query
    const query: any = { userId: new ObjectId(user.id) }
    
    if (type && type !== 'all') {
      query.type = type
    }
    
    // Get transactions with related item details
    const transactions = await transactionsCollection.aggregate<TransactionWithDetails>([
      { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user',
          pipeline: [{ $project: { password: 0 } }]
        }
      },
      {
        $lookup: {
          from: 'items',
          localField: 'relatedItemId',
          foreignField: '_id',
          as: 'relatedItem',
          pipeline: [
            { $project: { title: 1, images: 1 } }
          ]
        }
      },
      { $unwind: '$user' },
      {
        $unwind: {
          path: '$relatedItem',
          preserveNullAndEmptyArrays: true
        }
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit }
    ]).toArray()
    
    const total = await transactionsCollection.countDocuments(query)
    
    // Calculate summary statistics
    const summary = await transactionsCollection.aggregate([
      { $match: { userId: new ObjectId(user.id) } },
      {
        $group: {
          _id: '$type',
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]).toArray()
    
    const summaryData = {
      totalEarned: summary.find(s => s._id === 'earned')?.total || 0,
      totalSpent: Math.abs(summary.find(s => s._id === 'spent')?.total || 0),
      earnedCount: summary.find(s => s._id === 'earned')?.count || 0,
      spentCount: summary.find(s => s._id === 'spent')?.count || 0
    }
    
    return NextResponse.json({
      transactions,
      summary: summaryData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get transactions error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    const body: CreateTransactionInput = await request.json()
    const { userId, type, amount, reason, description, relatedItemId, relatedSwapId } = body
    
    const db = await getDatabase()
    const transactionsCollection = db.collection<Transaction>('transactions')
    const usersCollection = db.collection('users')
    
    // Validate user exists
    const targetUser = await usersCollection.findOne({ _id: new ObjectId(userId) })
    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    // Create transaction
    const newTransaction: Transaction = {
      userId: new ObjectId(userId),
      type,
      amount: type === 'spent' || type === 'penalty' ? -Math.abs(amount) : Math.abs(amount),
      reason,
      description,
      relatedItemId: relatedItemId ? new ObjectId(relatedItemId) : undefined,
      relatedSwapId: relatedSwapId ? new ObjectId(relatedSwapId) : undefined,
      createdAt: new Date()
    }
    
    // Start transaction to update both transaction and user points
    const session = db.client.startSession()
    
    try {
      await session.withTransaction(async () => {
        // Insert transaction
        await transactionsCollection.insertOne(newTransaction)
        
        // Update user points and totals
        const pointsUpdate: any = { points: newTransaction.amount }
        
        if (type === 'earned' || type === 'bonus') {
          pointsUpdate.totalPointsEarned = Math.abs(newTransaction.amount)
        } else if (type === 'spent' || type === 'penalty') {
          pointsUpdate.totalPointsSpent = Math.abs(newTransaction.amount)
        }
        
        await usersCollection.updateOne(
          { _id: new ObjectId(userId) },
          { $inc: pointsUpdate }
        )
      })
    } finally {
      await session.endSession()
    }
    
    return NextResponse.json(
      { 
        message: 'Transaction created successfully',
        transactionId: newTransaction._id
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Create transaction error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
