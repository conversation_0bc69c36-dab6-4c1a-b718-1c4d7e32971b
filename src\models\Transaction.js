// Transaction model definitions for ReWear platform

// Transaction types
export const TRANSACTION_TYPE = {
  EARNED: 'earned',
  SPENT: 'spent',
  BONUS: 'bonus',
  PENALTY: 'penalty'
}

// Transaction reasons
export const TRANSACTION_REASON = {
  ITEM_UPLOAD: 'item_upload',
  SWAP_COMPLETED: 'swap_completed',
  POINTS_REDEMPTION: 'points_redemption',
  ADMIN_BONUS: 'admin_bonus',
  ADMIN_PENALTY: 'admin_penalty',
  REFERRAL_BONUS: 'referral_bonus'
}

// Points configuration
export const POINTS_CONFIG = {
  ITEM_UPLOAD: 10, // Points earned for uploading an approved item
  SWAP_COMPLETED: 20, // Points earned for completing a swap
  REFERRAL_BONUS: 50, // Points earned for referring a new user
  WELCOME_BONUS: 100, // Points given to new users
}

// Create transaction structure
export const createTransaction = ({
  userId,
  type,
  amount,
  reason,
  description,
  relatedItemId = null,
  relatedSwapId = null,
  createdAt = new Date()
}) => ({
  userId,
  type,
  amount: type === TRANSACTION_TYPE.SPENT || type === TRANSACTION_TYPE.PENALTY 
    ? -Math.abs(amount) 
    : Math.abs(amount),
  reason,
  description,
  relatedItemId,
  relatedSwapId,
  createdAt
})

// Helper functions
export const getTransactionIcon = (type) => {
  switch (type) {
    case TRANSACTION_TYPE.EARNED:
    case TRANSACTION_TYPE.BONUS:
      return 'arrow-up-right'
    case TRANSACTION_TYPE.SPENT:
    case TRANSACTION_TYPE.PENALTY:
      return 'arrow-down-left'
    default:
      return 'coins'
  }
}

export const getTransactionColor = (type) => {
  switch (type) {
    case TRANSACTION_TYPE.EARNED:
    case TRANSACTION_TYPE.BONUS:
      return 'text-green-600'
    case TRANSACTION_TYPE.SPENT:
    case TRANSACTION_TYPE.PENALTY:
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

export const formatTransactionAmount = (amount, type) => {
  const prefix = type === TRANSACTION_TYPE.EARNED || type === TRANSACTION_TYPE.BONUS ? '+' : ''
  return `${prefix}${amount} pts`
}
