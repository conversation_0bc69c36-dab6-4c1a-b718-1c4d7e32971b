import { NextRequest, NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'
import { getUserFromToken } from '@/lib/auth'
import { SwapRequest } from '@/models/Item'
import { Transaction, POINTS_CONFIG } from '@/models/Transaction'
import { ObjectId } from 'mongodb'

interface RouteParams {
  params: { id: string }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const db = await getDatabase()
    const swapsCollection = db.collection<SwapRequest>('swapRequests')
    
    const swap = await swapsCollection.findOne({ _id: new ObjectId(params.id) })
    
    if (!swap) {
      return NextResponse.json(
        { error: 'Swap request not found' },
        { status: 404 }
      )
    }
    
    // Check if user is involved in this swap
    if (swap.requesterId.toString() !== user.id && swap.targetUserId.toString() !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }
    
    return NextResponse.json({ swap })
  } catch (error) {
    console.error('Get swap error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const { action } = await request.json() // 'accept', 'reject', 'cancel'
    
    const db = await getDatabase()
    const swapsCollection = db.collection<SwapRequest>('swapRequests')
    const itemsCollection = db.collection('items')
    const usersCollection = db.collection('users')
    const transactionsCollection = db.collection<Transaction>('transactions')
    
    const swap = await swapsCollection.findOne({ _id: new ObjectId(params.id) })
    
    if (!swap) {
      return NextResponse.json(
        { error: 'Swap request not found' },
        { status: 404 }
      )
    }
    
    if (swap.status !== 'pending') {
      return NextResponse.json(
        { error: 'Swap request is no longer pending' },
        { status: 400 }
      )
    }
    
    let updateData: any = { updatedAt: new Date() }
    
    if (action === 'accept') {
      // Only target user can accept
      if (swap.targetUserId.toString() !== user.id) {
        return NextResponse.json(
          { error: 'Only the target user can accept this swap' },
          { status: 403 }
        )
      }
      
      // Start transaction for swap completion
      const session = db.client.startSession()
      
      try {
        await session.withTransaction(async () => {
          if (swap.swapType === 'points') {
            // Points-based swap
            const requester = await usersCollection.findOne({ _id: swap.requesterId })
            const targetUser = await usersCollection.findOne({ _id: swap.targetUserId })
            
            if (!requester || !targetUser) {
              throw new Error('User not found')
            }
            
            if (requester.points < (swap.pointsOffered || 0)) {
              throw new Error('Insufficient points')
            }
            
            // Deduct points from requester
            await usersCollection.updateOne(
              { _id: swap.requesterId },
              { 
                $inc: { 
                  points: -(swap.pointsOffered || 0),
                  totalPointsSpent: swap.pointsOffered || 0,
                  swapCount: 1
                }
              }
            )
            
            // Add points to target user
            await usersCollection.updateOne(
              { _id: swap.targetUserId },
              { 
                $inc: { 
                  points: swap.pointsOffered || 0,
                  totalPointsEarned: swap.pointsOffered || 0,
                  swapCount: 1
                }
              }
            )
            
            // Create transaction records
            await transactionsCollection.insertMany([
              {
                userId: swap.requesterId,
                type: 'spent',
                amount: -(swap.pointsOffered || 0),
                reason: 'points_redemption',
                description: `Points spent on item swap`,
                relatedItemId: swap.targetItemId,
                relatedSwapId: swap._id,
                createdAt: new Date()
              },
              {
                userId: swap.targetUserId,
                type: 'earned',
                amount: swap.pointsOffered || 0,
                reason: 'swap_completed',
                description: `Points earned from item swap`,
                relatedItemId: swap.targetItemId,
                relatedSwapId: swap._id,
                createdAt: new Date()
              }
            ])
            
            // Update target item status
            await itemsCollection.updateOne(
              { _id: swap.targetItemId },
              { 
                status: 'swapped',
                swappedAt: new Date(),
                swappedWithUserId: swap.requesterId,
                swapType: 'points',
                updatedAt: new Date()
              }
            )
          } else {
            // Direct swap
            if (!swap.requesterItemId) {
              throw new Error('Requester item not found for direct swap')
            }
            
            // Update both users' swap counts
            await usersCollection.updateMany(
              { _id: { $in: [swap.requesterId, swap.targetUserId] } },
              { $inc: { swapCount: 1 } }
            )
            
            // Award points for completing swap
            const swapPoints = POINTS_CONFIG.SWAP_COMPLETED
            await usersCollection.updateMany(
              { _id: { $in: [swap.requesterId, swap.targetUserId] } },
              { 
                $inc: { 
                  points: swapPoints,
                  totalPointsEarned: swapPoints
                }
              }
            )
            
            // Create transaction records for both users
            await transactionsCollection.insertMany([
              {
                userId: swap.requesterId,
                type: 'earned',
                amount: swapPoints,
                reason: 'swap_completed',
                description: `Points earned for completing direct swap`,
                relatedItemId: swap.targetItemId,
                relatedSwapId: swap._id,
                createdAt: new Date()
              },
              {
                userId: swap.targetUserId,
                type: 'earned',
                amount: swapPoints,
                reason: 'swap_completed',
                description: `Points earned for completing direct swap`,
                relatedItemId: swap.requesterItemId,
                relatedSwapId: swap._id,
                createdAt: new Date()
              }
            ])
            
            // Update both items status
            await itemsCollection.updateMany(
              { _id: { $in: [swap.targetItemId, swap.requesterItemId] } },
              { 
                status: 'swapped',
                swappedAt: new Date(),
                swapType: 'direct',
                updatedAt: new Date()
              }
            )
            
            // Cross-reference the swapped items
            await itemsCollection.updateOne(
              { _id: swap.targetItemId },
              { 
                swappedWithUserId: swap.requesterId,
                swappedWithItemId: swap.requesterItemId
              }
            )
            
            await itemsCollection.updateOne(
              { _id: swap.requesterItemId },
              { 
                swappedWithUserId: swap.targetUserId,
                swappedWithItemId: swap.targetItemId
              }
            )
          }
          
          // Update swap status
          await swapsCollection.updateOne(
            { _id: swap._id },
            { 
              status: 'completed',
              completedAt: new Date(),
              updatedAt: new Date()
            }
          )
        })
        
        updateData.status = 'completed'
        updateData.completedAt = new Date()
        
      } finally {
        await session.endSession()
      }
      
    } else if (action === 'reject') {
      // Only target user can reject
      if (swap.targetUserId.toString() !== user.id) {
        return NextResponse.json(
          { error: 'Only the target user can reject this swap' },
          { status: 403 }
        )
      }
      
      updateData.status = 'rejected'
      
    } else if (action === 'cancel') {
      // Only requester can cancel
      if (swap.requesterId.toString() !== user.id) {
        return NextResponse.json(
          { error: 'Only the requester can cancel this swap' },
          { status: 403 }
        )
      }
      
      updateData.status = 'cancelled'
      
    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      )
    }
    
    await swapsCollection.updateOne(
      { _id: new ObjectId(params.id) },
      { $set: updateData }
    )
    
    return NextResponse.json({ 
      message: `Swap request ${action}ed successfully`,
      status: updateData.status
    })
    
  } catch (error) {
    console.error('Update swap error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
