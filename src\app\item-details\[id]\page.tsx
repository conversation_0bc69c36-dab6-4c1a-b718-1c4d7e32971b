'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ItemWithUser } from '@/models/Item'
import { useAuth } from '@/contexts/auth-context'
import { formatDate, capitalizeFirst, getConditionColor } from '@/lib/utils'
import {
  ArrowLeft,
  User,
  Calendar,
  Tag,
  Package,
  Coins,
  RefreshCw,
  Heart,
  AlertCircle,
  CheckCircle,
  MessageCircle
} from 'lucide-react'

export default function ItemDetailsPage() {
  const [item, setItem] = useState<ItemWithUser | null>(null)
  const [userItems, setUserItems] = useState<ItemWithUser[]>([])
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [showSwapModal, setShowSwapModal] = useState(false)
  const [swapType, setSwapType] = useState<'direct' | 'points'>('direct')
  const [selectedItemId, setSelectedItemId] = useState<string>('')
  const [pointsOffered, setPointsOffered] = useState<number>(0)
  const [swapMessage, setSwapMessage] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const params = useParams()
  const { user } = useAuth()

  useEffect(() => {
    if (params.id) {
      fetchItem(params.id as string)
    }
  }, [params.id])

  useEffect(() => {
    if (user && showSwapModal) {
      fetchUserItems()
    }
  }, [user, showSwapModal])

  const fetchItem = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/items/${id}`)
      if (response.ok) {
        const data = await response.json()
        setItem(data.item)
      } else {
        console.error('Item not found')
      }
    } catch (error) {
      console.error('Error fetching item:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUserItems = async () => {
    if (!user) return

    try {
      const response = await fetch(`/api/items?userId=${user.id}&status=approved`)
      if (response.ok) {
        const data = await response.json()
        setUserItems(data.items)
      }
    } catch (error) {
      console.error('Error fetching user items:', error)
    }
  }

  const handleSwapRequest = async () => {
    if (!item || !user) return

    try {
      setSubmitting(true)

      const swapData = {
        targetItemId: item._id?.toString(),
        swapType,
        ...(swapType === 'direct' && { requesterItemId: selectedItemId }),
        ...(swapType === 'points' && { pointsOffered }),
        ...(swapMessage && { message: swapMessage })
      }

      const response = await fetch('/api/swaps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(swapData),
      })

      if (response.ok) {
        alert('Swap request sent successfully!')
        setShowSwapModal(false)
        resetSwapForm()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to send swap request')
      }
    } catch (error) {
      console.error('Error sending swap request:', error)
      alert('Failed to send swap request')
    } finally {
      setSubmitting(false)
    }
  }

  const resetSwapForm = () => {
    setSwapType('direct')
    setSelectedItemId('')
    setPointsOffered(0)
    setSwapMessage('')
  }

  const canUserSwap = () => {
    if (!user || !item) return false
    if (item.userId.toString() === user.id) return false
    if (item.status !== 'approved') return false
    return true
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="aspect-square bg-gray-200 rounded-lg"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!item) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Item not found</h1>
          <Link href="/">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Items
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Gallery */}
          <div className="space-y-4">
            <div className="aspect-square relative rounded-lg overflow-hidden bg-white">
              <Image
                src={item.images[currentImageIndex] || '/placeholder-image.svg'}
                alt={item.title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
            
            {item.images.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {item.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`aspect-square relative rounded-md overflow-hidden border-2 ${
                      currentImageIndex === index ? 'border-primary' : 'border-gray-200'
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${item.title} ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 25vw, 12.5vw"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Item Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{item.title}</h1>
              <div className="flex items-center space-x-2 mb-4">
                <Badge className={getConditionColor(item.condition)}>
                  {capitalizeFirst(item.condition)}
                </Badge>
                <Badge variant="outline">
                  {capitalizeFirst(item.category)}
                </Badge>
              </div>
              <div className="flex items-center gap-2 text-3xl font-bold text-primary mb-4">
                <Coins className="w-8 h-8" />
                <span>{item.pointsValue} points</span>
              </div>

              {/* Availability Status */}
              <div className="flex items-center gap-2 mb-4">
                {item.status === 'approved' ? (
                  <>
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-green-600 font-medium">Available for swap</span>
                  </>
                ) : item.status === 'swapped' ? (
                  <>
                    <AlertCircle className="w-5 h-5 text-orange-500" />
                    <span className="text-orange-600 font-medium">Already swapped</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-5 h-5 text-gray-500" />
                    <span className="text-gray-600 font-medium">Not available</span>
                  </>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg mb-2">Description</h3>
                <p className="text-gray-600">{item.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">Size</h4>
                  <p className="text-gray-600">{item.size}</p>
                </div>
                {item.brand && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Brand</h4>
                    <p className="text-gray-600">{item.brand}</p>
                  </div>
                )}
                {item.originalPrice && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Original Price</h4>
                    <p className="text-gray-600">${item.originalPrice}</p>
                  </div>
                )}
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">Posted</h4>
                  <p className="text-gray-600">{formatDate(item.createdAt)}</p>
                </div>
              </div>

              {item.tags && item.tags.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {item.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Owner Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                    {item.user.name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <p className="font-medium">{item.user.name}</p>
                    <p className="text-sm text-gray-500">ReWear Member</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="space-y-3">
              {canUserSwap() ? (
                <>
                  <Button
                    size="lg"
                    className="w-full"
                    onClick={() => setShowSwapModal(true)}
                  >
                    <RefreshCw className="w-5 h-5 mr-2" />
                    Request Swap
                  </Button>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => {
                        setSwapType('points')
                        setPointsOffered(item.pointsValue)
                        setShowSwapModal(true)
                      }}
                    >
                      <Coins className="w-4 h-4 mr-2" />
                      Use Points
                    </Button>
                    <Button variant="outline" size="lg">
                      <Heart className="w-4 h-4 mr-2" />
                      Save Item
                    </Button>
                  </div>
                </>
              ) : !user ? (
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-gray-600 mb-3">Sign in to request swaps</p>
                  <Link href="/login">
                    <Button size="lg">Sign In</Button>
                  </Link>
                </div>
              ) : item?.userId.toString() === user.id ? (
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-600 font-medium">This is your item</p>
                  <p className="text-sm text-blue-500 mt-1">You can manage it from your dashboard</p>
                </div>
              ) : (
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">This item is not available for swap</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Swap Request Modal */}
        {showSwapModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Request Swap</h3>
                  <button
                    onClick={() => {
                      setShowSwapModal(false)
                      resetSwapForm()
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Swap Type Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Swap Type
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => setSwapType('direct')}
                        className={`p-3 border rounded-lg text-sm font-medium ${
                          swapType === 'direct'
                            ? 'border-primary bg-primary text-white'
                            : 'border-gray-200 text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <RefreshCw className="w-4 h-4 mx-auto mb-1" />
                        Direct Swap
                      </button>
                      <button
                        onClick={() => {
                          setSwapType('points')
                          setPointsOffered(item?.pointsValue || 0)
                        }}
                        className={`p-3 border rounded-lg text-sm font-medium ${
                          swapType === 'points'
                            ? 'border-primary bg-primary text-white'
                            : 'border-gray-200 text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <Coins className="w-4 h-4 mx-auto mb-1" />
                        Use Points
                      </button>
                    </div>
                  </div>

                  {/* Direct Swap - Item Selection */}
                  {swapType === 'direct' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Your Item to Swap
                      </label>
                      {userItems.length > 0 ? (
                        <select
                          value={selectedItemId}
                          onChange={(e) => setSelectedItemId(e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">Choose an item...</option>
                          {userItems.map((userItem) => (
                            <option key={userItem._id?.toString()} value={userItem._id?.toString()}>
                              {userItem.title} - {userItem.pointsValue} pts
                            </option>
                          ))}
                        </select>
                      ) : (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <p className="text-gray-600 text-sm">
                            You don't have any approved items to swap.
                          </p>
                          <Link href="/upload-item">
                            <Button size="sm" className="mt-2">
                              Upload an Item
                            </Button>
                          </Link>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Points Swap - Points Input */}
                  {swapType === 'points' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Points to Offer
                      </label>
                      <div className="flex items-center gap-2">
                        <input
                          type="number"
                          value={pointsOffered}
                          onChange={(e) => setPointsOffered(parseInt(e.target.value) || 0)}
                          min={item?.pointsValue || 0}
                          className="flex-1 p-2 border border-gray-300 rounded-md"
                        />
                        <span className="text-sm text-gray-500">
                          (Required: {item?.pointsValue} pts)
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Your balance: {user?.points || 0} points
                      </p>
                    </div>
                  )}

                  {/* Message */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Message (Optional)
                    </label>
                    <textarea
                      value={swapMessage}
                      onChange={(e) => setSwapMessage(e.target.value)}
                      placeholder="Add a message to the owner..."
                      className="w-full p-2 border border-gray-300 rounded-md h-20 resize-none"
                      maxLength={500}
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowSwapModal(false)
                        resetSwapForm()
                      }}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSwapRequest}
                      disabled={
                        submitting ||
                        (swapType === 'direct' && !selectedItemId) ||
                        (swapType === 'points' && (pointsOffered < (item?.pointsValue || 0) || (user?.points || 0) < pointsOffered))
                      }
                      className="flex-1"
                    >
                      {submitting ? 'Sending...' : 'Send Request'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
