# ReWear - Sustainable Fashion Exchange Platform

## 🌱 About ReWear

ReWear is a comprehensive web-based platform that enables users to exchange unused clothing through direct swaps or a point-based redemption system. Our mission is to promote sustainable fashion and reduce textile waste by encouraging users to reuse wearable garments instead of discarding them.

## ✨ Key Features

### 🔐 User Authentication
- Email/password signup and login using Spacekey library
- Secure session management
- User profile management
- Welcome bonus points for new users

### 🏠 Landing Page
- Platform introduction with sustainability focus
- Call-to-action buttons: "Start Swapping", "Browse Items", "List an Item"
- Featured items carousel
- How ReWear Works section with feature highlights

### 📊 User Dashboard
- Profile details and points balance display
- Uploaded items overview with status tracking
- Ongoing and completed swaps list
- Transaction history
- Comprehensive statistics

### 👕 Item Management
- Upload images with drag-and-drop interface
- Enter title, description, category, type, size, condition, and tags
- Points-based valuation system
- Admin moderation and approval process

### 🔄 Swap System
- **Direct Swaps**: Trade items directly with other users
- **Points Redemption**: Use earned points to redeem desired items
- Swap request management
- Real-time status updates

### 👑 Admin Panel
- Moderate and approve/reject item listings
- Remove inappropriate or spam items
- User management and points administration
- Swap oversight and management
- Comprehensive analytics dashboard

## 🛠 Technology Stack

- **Frontend**: HTML5, Tailwind CSS, JavaScript
- **Backend**: Node.js, Express.js
- **Database**: MongoDB
- **Authentication**: Spacekey library
- **Image Handling**: Sharp for optimization
- **UI Components**: Custom components with Tailwind CSS
- **Icons**: Lucide React icons

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB database
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd Team-1757-PS3-ReWear
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```
MONGODB_URI=mongodb://localhost:27017/rewear
JWT_SECRET=your-secret-key
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
```

4. Initialize the database:
```bash
npm run init-db
```

5. Start the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📱 Responsive Design

ReWear is fully responsive and optimized for:
- Desktop computers (1024px+)
- Tablets (768px - 1023px)
- Mobile phones (320px - 767px)

All components use Tailwind CSS responsive utilities for seamless experience across devices.

## 🎯 Core Functionality

### Points System
- **Welcome Bonus**: 100 points for new users
- **Item Upload**: 10 points for approved items
- **Swap Completion**: 20 points for successful swaps
- **Referral Bonus**: 50 points for referring new users

### Swap Process
1. **Browse Items**: Users can browse available items
2. **Request Swap**: Choose direct swap or points redemption
3. **Negotiation**: Item owners can accept/reject requests
4. **Completion**: Automatic points transfer and status updates

### Admin Features
- Item moderation queue
- User management
- Points administration
- Swap oversight
- Platform analytics

## 🧪 Testing

Run the test suite:
```bash
node test-rewear.js
```

This validates:
- Model creation and validation
- Points system calculations
- Status color mappings
- Core functionality

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin panel
│   ├── api/               # API routes
│   ├── item-details/      # Item detail pages
│   ├── login/             # Authentication pages
│   ├── profile/           # User dashboard
│   └── upload-item/       # Item upload
├── components/            # Reusable components
│   ├── home/             # Homepage components
│   ├── items/            # Item-related components
│   ├── layout/           # Layout components
│   ├── modals/           # Modal components
│   └── ui/               # UI components
├── contexts/             # React contexts
├── lib/                  # Utility libraries
├── models/               # Data models (JavaScript)
└── middleware.ts         # Next.js middleware
```

## 🌍 Environmental Impact

ReWear contributes to sustainability by:
- Reducing textile waste through item reuse
- Promoting circular fashion economy
- Encouraging conscious consumption
- Building community around sustainable practices

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**ReWear** - *Transforming fashion, one swap at a time* 🌱👕♻️
