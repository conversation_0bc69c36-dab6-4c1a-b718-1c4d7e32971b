'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { ItemWithUser, SwapRequestWithDetails } from '@/models/Item'
import { TransactionWithDetails } from '@/models/Transaction'
import { User } from '@/models/User'
import { formatDate, capitalizeFirst, getStatusColor, getConditionColor } from '@/lib/utils'
import {
  CheckCircle,
  XCircle,
  Clock,
  Users,
  Package,
  AlertTriangle,
  Eye,
  Trash2,
  RefreshCw,
  Coins,
  TrendingUp,
  Settings,
  UserCheck,
  Activity
} from 'lucide-react'

export default function AdminPage() {
  const [items, setItems] = useState<ItemWithUser[]>([])
  const [swaps, setSwaps] = useState<SwapRequestWithDetails[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [transactions, setTransactions] = useState<TransactionWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'items' | 'swaps' | 'users' | 'points'>('overview')
  const [itemsFilter, setItemsFilter] = useState<'pending' | 'all' | 'approved' | 'rejected'>('pending')
  const [selectedItem, setSelectedItem] = useState<ItemWithUser | null>(null)
  const [rejectionReason, setRejectionReason] = useState('')
  const [actionLoading, setActionLoading] = useState(false)
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalItems: 0,
    totalSwaps: 0,
    pendingItems: 0,
    activeSwaps: 0,
    totalPointsInCirculation: 0
  })

  useEffect(() => {
    if (activeTab === 'overview') {
      fetchOverviewData()
    } else if (activeTab === 'items') {
      fetchItems()
    } else if (activeTab === 'swaps') {
      fetchSwaps()
    } else if (activeTab === 'users') {
      fetchUsers()
    } else if (activeTab === 'points') {
      fetchTransactions()
    }
  }, [activeTab, itemsFilter])

  const fetchOverviewData = async () => {
    try {
      setLoading(true)
      // Fetch stats from multiple endpoints
      const [itemsRes, swapsRes, usersRes] = await Promise.all([
        fetch('/api/items?limit=1'),
        fetch('/api/swaps?limit=1'),
        fetch('/api/admin/users?limit=1')
      ])

      if (itemsRes.ok && swapsRes.ok && usersRes.ok) {
        const [itemsData, swapsData, usersData] = await Promise.all([
          itemsRes.json(),
          swapsRes.json(),
          usersRes.json()
        ])

        setStats({
          totalUsers: usersData.pagination?.total || 0,
          totalItems: itemsData.pagination?.total || 0,
          totalSwaps: swapsData.pagination?.total || 0,
          pendingItems: itemsData.items?.filter((item: any) => item.status === 'pending').length || 0,
          activeSwaps: swapsData.swaps?.filter((swap: any) => swap.status === 'pending').length || 0,
          totalPointsInCirculation: usersData.totalPoints || 0
        })
      }
    } catch (error) {
      console.error('Error fetching overview data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchItems = async () => {
    try {
      setLoading(true)
      const status = itemsFilter === 'all' ? '' : itemsFilter
      const response = await fetch(`/api/items?status=${status}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        setItems(data.items)
      }
    } catch (error) {
      console.error('Error fetching items:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSwaps = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/swaps?type=all&limit=50')
      if (response.ok) {
        const data = await response.json()
        setSwaps(data.swaps)
      }
    } catch (error) {
      console.error('Error fetching swaps:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/users?limit=50')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/transactions?limit=50')
      if (response.ok) {
        const data = await response.json()
        setTransactions(data.transactions)
      }
    } catch (error) {
      console.error('Error fetching transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (itemId: string) => {
    try {
      setActionLoading(true)
      const response = await fetch(`/api/admin/items/${itemId}/approve`, {
        method: 'POST',
      })

      if (response.ok) {
        await fetchItems()
        setSelectedItem(null)
      } else {
        console.error('Failed to approve item')
      }
    } catch (error) {
      console.error('Error approving item:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const handleReject = async (itemId: string) => {
    try {
      setActionLoading(true)
      const response = await fetch(`/api/admin/items/${itemId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: rejectionReason }),
      })

      if (response.ok) {
        await fetchItems()
        setSelectedItem(null)
        setRejectionReason('')
      } else {
        console.error('Failed to reject item')
      }
    } catch (error) {
      console.error('Error rejecting item:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const handleDelete = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
      return
    }

    try {
      setActionLoading(true)
      const response = await fetch(`/api/items/${itemId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchItems()
        setSelectedItem(null)
      } else {
        console.error('Failed to delete item')
      }
    } catch (error) {
      console.error('Error deleting item:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const getItemCounts = () => {
    return {
      pending: items.filter(item => item.status === 'pending').length,
      approved: items.filter(item => item.status === 'approved').length,
      rejected: items.filter(item => item.status === 'rejected').length,
      total: items.length,
    }
  }

  const counts = getItemCounts()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage the ReWear platform, swaps, and community</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white p-1 rounded-lg shadow-sm">
          {[
            { key: 'overview', label: 'Overview', icon: TrendingUp },
            { key: 'items', label: 'Items', icon: Package },
            { key: 'swaps', label: 'Swaps', icon: RefreshCw },
            { key: 'users', label: 'Users', icon: Users },
            { key: 'points', label: 'Points', icon: Coins },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex items-center gap-2 flex-1 px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold text-blue-600">{stats.totalUsers}</p>
                    </div>
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Items</p>
                      <p className="text-2xl font-bold text-green-600">{stats.totalItems}</p>
                    </div>
                    <Package className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Swaps</p>
                      <p className="text-2xl font-bold text-purple-600">{stats.totalSwaps}</p>
                    </div>
                    <RefreshCw className="w-8 h-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending Items</p>
                      <p className="text-2xl font-bold text-orange-600">{stats.pendingItems}</p>
                    </div>
                    <Clock className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Swaps</p>
                      <p className="text-2xl font-bold text-red-600">{stats.activeSwaps}</p>
                    </div>
                    <Activity className="w-8 h-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Points in Circulation</p>
                      <p className="text-2xl font-bold text-primary">{stats.totalPointsInCirculation}</p>
                    </div>
                    <Coins className="w-8 h-8 text-primary" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
        {/* Items Tab */}
        {activeTab === 'items' && (
          <Card>
            <CardHeader>
              <CardTitle>Items Management</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Items Filter Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
                {[
                  { key: 'pending', label: 'Pending', count: items.filter(item => item.status === 'pending').length },
                  { key: 'approved', label: 'Approved', count: items.filter(item => item.status === 'approved').length },
                  { key: 'rejected', label: 'Rejected', count: items.filter(item => item.status === 'rejected').length },
                  { key: 'all', label: 'All', count: items.length },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setItemsFilter(tab.key as any)}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      itemsFilter === tab.key
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>

              {/* Items List */}
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse flex space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : items.length > 0 ? (
                <div className="space-y-4">
                  {items.map((item) => (
                    <div key={item._id?.toString()} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                          <Image
                            src={item.images[0] || '/placeholder-image.svg'}
                            alt={item.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="font-medium">{item.title}</h3>
                          <p className="text-sm text-gray-500">by {item.user.name}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getStatusColor(item.status)}>
                              {capitalizeFirst(item.status)}
                            </Badge>
                            <span className="text-sm text-gray-500 flex items-center gap-1">
                              <Coins className="w-3 h-3" />
                              {item.pointsValue} pts
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedItem(item)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {item.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => handleApprove(item._id?.toString() || '')}
                              disabled={actionLoading}
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                setSelectedItem(item)
                                // You can add a reject modal here
                              }}
                              disabled={actionLoading}
                            >
                              <XCircle className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No items found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Items List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Items Management</CardTitle>
                
                {/* Tabs */}
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: 'pending', label: 'Pending', count: counts.pending },
                    { key: 'all', label: 'All', count: counts.total },
                    { key: 'approved', label: 'Approved', count: counts.approved },
                    { key: 'rejected', label: 'Rejected', count: counts.rejected },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setActiveTab(tab.key as any)}
                      className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeTab === tab.key
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {tab.label} ({tab.count})
                    </button>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="animate-pulse flex space-x-4 p-4 border rounded-lg">
                        <div className="w-16 h-16 bg-gray-200 rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : items.length > 0 ? (
                  <div className="space-y-4">
                    {items.map((item) => (
                      <div
                        key={item._id?.toString()}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedItem?._id === item._id
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedItem(item)}
                      >
                        <div className="flex space-x-4">
                          <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                            <Image
                              src={item.images[0] || '/placeholder-image.svg'}
                              alt={item.title}
                              fill
                              className="object-cover"
                              sizes="64px"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-medium text-gray-900 truncate">{item.title}</h3>
                                <p className="text-sm text-gray-500">by {item.user.name}</p>
                                <p className="text-sm font-medium text-primary">{formatPrice(item.sellingPrice)}</p>
                              </div>
                              <div className="flex flex-col items-end space-y-1">
                                <Badge className={getStatusColor(item.status)}>
                                  {capitalizeFirst(item.status)}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {formatDate(item.createdAt)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No items found</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Item Details */}
          <div className="lg:col-span-1">
            {selectedItem ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Item Details</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedItem(null)}
                    >
                      <XCircle className="w-4 h-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="aspect-square relative rounded-lg overflow-hidden">
                    <Image
                      src={selectedItem.images[0] || '/placeholder-image.svg'}
                      alt={selectedItem.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 33vw"
                    />
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg">{selectedItem.title}</h3>
                    <p className="text-gray-600 text-sm mt-1">{selectedItem.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Price:</span>
                      <p>{formatPrice(selectedItem.sellingPrice)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Category:</span>
                      <p>{capitalizeFirst(selectedItem.category)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Condition:</span>
                      <p>{capitalizeFirst(selectedItem.condition)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Size:</span>
                      <p>{selectedItem.size}</p>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-sm">Seller:</span>
                    <p className="text-sm">{selectedItem.user.name}</p>
                    <p className="text-sm text-gray-500">{selectedItem.user.email}</p>
                  </div>

                  {selectedItem.status === 'rejected' && selectedItem.rejectionReason && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm font-medium text-red-800">Rejection Reason:</p>
                      <p className="text-sm text-red-600">{selectedItem.rejectionReason}</p>
                    </div>
                  )}

                  {selectedItem.status === 'pending' && (
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Rejection Reason (optional)</label>
                        <Textarea
                          placeholder="Enter reason for rejection..."
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                          rows={3}
                        />
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => handleApprove(selectedItem._id!.toString())}
                          disabled={actionLoading}
                          className="flex-1"
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Approve
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => handleReject(selectedItem._id!.toString())}
                          disabled={actionLoading}
                          className="flex-1"
                        >
                          <XCircle className="w-4 h-4 mr-2" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t">
                    <Button
                      variant="destructive"
                      onClick={() => handleDelete(selectedItem._id!.toString())}
                      disabled={actionLoading}
                      className="w-full"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Item
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Eye className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Select an item to view details</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
