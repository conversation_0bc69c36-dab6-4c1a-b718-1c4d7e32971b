'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { ItemWithUser, SwapRequestWithDetails } from '@/models/Item'
import { TransactionWithDetails } from '@/models/Transaction'
import { User } from '@/models/User'
import { formatDate, capitalizeFirst, getStatusColor, getConditionColor } from '@/lib/utils'
import {
  CheckCircle,
  XCircle,
  Clock,
  Users,
  Package,
  AlertTriangle,
  Eye,
  Trash2,
  RefreshCw,
  Coins,
  TrendingUp,
  Settings,
  UserCheck,
  Activity
} from 'lucide-react'

export default function AdminPage() {
  const [items, setItems] = useState<ItemWithUser[]>([])
  const [swaps, setSwaps] = useState<SwapRequestWithDetails[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [transactions, setTransactions] = useState<TransactionWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'items' | 'swaps' | 'users' | 'points'>('overview')
  const [itemsFilter, setItemsFilter] = useState<'pending' | 'all' | 'approved' | 'rejected'>('pending')
  const [selectedItem, setSelectedItem] = useState<ItemWithUser | null>(null)
  const [rejectionReason, setRejectionReason] = useState('')
  const [actionLoading, setActionLoading] = useState(false)
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalItems: 0,
    totalSwaps: 0,
    pendingItems: 0,
    activeSwaps: 0,
    totalPointsInCirculation: 0
  })

  useEffect(() => {
    if (activeTab === 'overview') {
      fetchOverviewData()
    } else if (activeTab === 'items') {
      fetchItems()
    } else if (activeTab === 'swaps') {
      fetchSwaps()
    } else if (activeTab === 'users') {
      fetchUsers()
    } else if (activeTab === 'points') {
      fetchTransactions()
    }
  }, [activeTab, itemsFilter])

  const fetchOverviewData = async () => {
    try {
      setLoading(true)
      // Fetch stats from multiple endpoints
      const [itemsRes, swapsRes, usersRes] = await Promise.all([
        fetch('/api/items?limit=1'),
        fetch('/api/swaps?limit=1'),
        fetch('/api/admin/users?limit=1')
      ])

      if (itemsRes.ok && swapsRes.ok && usersRes.ok) {
        const [itemsData, swapsData, usersData] = await Promise.all([
          itemsRes.json(),
          swapsRes.json(),
          usersRes.json()
        ])

        setStats({
          totalUsers: usersData.pagination?.total || 0,
          totalItems: itemsData.pagination?.total || 0,
          totalSwaps: swapsData.pagination?.total || 0,
          pendingItems: itemsData.items?.filter((item: any) => item.status === 'pending').length || 0,
          activeSwaps: swapsData.swaps?.filter((swap: any) => swap.status === 'pending').length || 0,
          totalPointsInCirculation: usersData.totalPoints || 0
        })
      }
    } catch (error) {
      console.error('Error fetching overview data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchItems = async () => {
    try {
      setLoading(true)
      const status = itemsFilter === 'all' ? '' : itemsFilter
      const response = await fetch(`/api/items?status=${status}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        setItems(data.items)
      }
    } catch (error) {
      console.error('Error fetching items:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSwaps = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/swaps?type=all&limit=50')
      if (response.ok) {
        const data = await response.json()
        setSwaps(data.swaps)
      }
    } catch (error) {
      console.error('Error fetching swaps:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/users?limit=50')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/transactions?limit=50')
      if (response.ok) {
        const data = await response.json()
        setTransactions(data.transactions)
      }
    } catch (error) {
      console.error('Error fetching transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (itemId: string) => {
    try {
      setActionLoading(true)
      const response = await fetch(`/api/admin/items/${itemId}/approve`, {
        method: 'POST',
      })

      if (response.ok) {
        await fetchItems()
        setSelectedItem(null)
      } else {
        console.error('Failed to approve item')
      }
    } catch (error) {
      console.error('Error approving item:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const handleReject = async (itemId: string) => {
    try {
      setActionLoading(true)
      const response = await fetch(`/api/admin/items/${itemId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: rejectionReason }),
      })

      if (response.ok) {
        await fetchItems()
        setSelectedItem(null)
        setRejectionReason('')
      } else {
        console.error('Failed to reject item')
      }
    } catch (error) {
      console.error('Error rejecting item:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const handleDelete = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
      return
    }

    try {
      setActionLoading(true)
      const response = await fetch(`/api/items/${itemId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchItems()
        setSelectedItem(null)
      } else {
        console.error('Failed to delete item')
      }
    } catch (error) {
      console.error('Error deleting item:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const getItemCounts = () => {
    return {
      pending: items.filter(item => item.status === 'pending').length,
      approved: items.filter(item => item.status === 'approved').length,
      rejected: items.filter(item => item.status === 'rejected').length,
      total: items.length,
    }
  }

  const counts = getItemCounts()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage the ReWear platform, swaps, and community</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white p-1 rounded-lg shadow-sm">
          {[
            { key: 'overview', label: 'Overview', icon: TrendingUp },
            { key: 'items', label: 'Items', icon: Package },
            { key: 'swaps', label: 'Swaps', icon: RefreshCw },
            { key: 'users', label: 'Users', icon: Users },
            { key: 'points', label: 'Points', icon: Coins },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex items-center gap-2 flex-1 px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold text-blue-600">{stats.totalUsers}</p>
                    </div>
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Items</p>
                      <p className="text-2xl font-bold text-green-600">{stats.totalItems}</p>
                    </div>
                    <Package className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Swaps</p>
                      <p className="text-2xl font-bold text-purple-600">{stats.totalSwaps}</p>
                    </div>
                    <RefreshCw className="w-8 h-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending Items</p>
                      <p className="text-2xl font-bold text-orange-600">{stats.pendingItems}</p>
                    </div>
                    <Clock className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Swaps</p>
                      <p className="text-2xl font-bold text-red-600">{stats.activeSwaps}</p>
                    </div>
                    <Activity className="w-8 h-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Points in Circulation</p>
                      <p className="text-2xl font-bold text-primary">{stats.totalPointsInCirculation}</p>
                    </div>
                    <Coins className="w-8 h-8 text-primary" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
        {/* Items Tab */}
        {activeTab === 'items' && (
          <Card>
            <CardHeader>
              <CardTitle>Items Management</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Items Filter Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
                {[
                  { key: 'pending', label: 'Pending', count: items.filter(item => item.status === 'pending').length },
                  { key: 'approved', label: 'Approved', count: items.filter(item => item.status === 'approved').length },
                  { key: 'rejected', label: 'Rejected', count: items.filter(item => item.status === 'rejected').length },
                  { key: 'all', label: 'All', count: items.length },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setItemsFilter(tab.key as any)}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      itemsFilter === tab.key
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>

              {/* Items List */}
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse flex space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : items.length > 0 ? (
                <div className="space-y-4">
                  {items.map((item) => (
                    <div key={item._id?.toString()} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                          <Image
                            src={item.images[0] || '/placeholder-image.svg'}
                            alt={item.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="font-medium">{item.title}</h3>
                          <p className="text-sm text-gray-500">by {item.user.name}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getStatusColor(item.status)}>
                              {capitalizeFirst(item.status)}
                            </Badge>
                            <span className="text-sm text-gray-500 flex items-center gap-1">
                              <Coins className="w-3 h-3" />
                              {item.pointsValue} pts
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedItem(item)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {item.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => handleApprove(item._id?.toString() || '')}
                              disabled={actionLoading}
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                setSelectedItem(item)
                                // You can add a reject modal here
                              }}
                              disabled={actionLoading}
                            >
                              <XCircle className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No items found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Swaps Tab */}
        {activeTab === 'swaps' && (
          <Card>
            <CardHeader>
              <CardTitle>Swap Requests Management</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse p-4 border rounded-lg">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : swaps.length > 0 ? (
                <div className="space-y-4">
                  {swaps.map((swap) => (
                    <div key={swap._id?.toString()} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(swap.status)}>
                            {capitalizeFirst(swap.status)}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {swap.swapType === 'direct' ? 'Direct Swap' : 'Points Swap'}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {formatDate(swap.createdAt)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="font-medium text-gray-900">Requester</p>
                          <p className="text-gray-600">{swap.requester.name}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Target User</p>
                          <p className="text-gray-600">{swap.targetUser.name}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Target Item</p>
                          <p className="text-gray-600">{swap.targetItem.title}</p>
                        </div>
                        {swap.swapType === 'points' && (
                          <div>
                            <p className="font-medium text-gray-900">Points Offered</p>
                            <p className="text-gray-600 flex items-center gap-1">
                              <Coins className="w-4 h-4" />
                              {swap.pointsOffered}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <RefreshCw className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No swap requests found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <Card>
            <CardHeader>
              <CardTitle>Users Management</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : users.length > 0 ? (
                <div className="space-y-4">
                  {users.map((user) => (
                    <div key={user._id?.toString()} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                          {user.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <h3 className="font-medium">{user.name}</h3>
                          <p className="text-sm text-gray-500">{user.email}</p>
                          <div className="flex items-center gap-4 mt-1 text-sm">
                            <span className="flex items-center gap-1">
                              <Coins className="w-3 h-3" />
                              {user.points || 0} pts
                            </span>
                            <span className="flex items-center gap-1">
                              <RefreshCw className="w-3 h-3" />
                              {user.swapCount || 0} swaps
                            </span>
                            <Badge variant={user.isActive ? "default" : "secondary"}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        Joined {formatDate(user.createdAt)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No users found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Points Tab */}
        {activeTab === 'points' && (
          <Card>
            <CardHeader>
              <CardTitle>Points & Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse p-4 border rounded-lg">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactions.map((transaction, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {transaction.type === 'earned' ? (
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <Coins className="w-4 h-4 text-green-600" />
                          </div>
                        ) : (
                          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <Coins className="w-4 h-4 text-red-600" />
                          </div>
                        )}
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-gray-500">
                            {transaction.user.name} • {formatDate(transaction.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className={`font-semibold ${
                        transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'earned' ? '+' : ''}{transaction.amount} pts
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Coins className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No transactions found</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
