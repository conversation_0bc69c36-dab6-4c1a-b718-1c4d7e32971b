'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import ItemCard from '@/components/items/item-card'
import { User } from '@/models/User'
import { ItemWithUser, SwapRequestWithDetails } from '@/models/Item'
import { TransactionWithDetails } from '@/models/Transaction'
import { formatDate, getStatusColor, capitalizeFirst } from '@/lib/utils'
import {
  User as UserIcon,
  Mail,
  Phone,
  MapPin,
  Package,
  Plus,
  Coins,
  RefreshCw,
  TrendingUp,
  History,
  ArrowUpRight,
  ArrowDownLeft,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import Link from 'next/link'

export default function ProfilePage() {
  const [user, setUser] = useState<User | null>(null)
  const [userItems, setUserItems] = useState<ItemWithUser[]>([])
  const [swapRequests, setSwapRequests] = useState<SwapRequestWithDetails[]>([])
  const [transactions, setTransactions] = useState<TransactionWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'items' | 'swaps' | 'points'>('overview')
  const [itemsFilter, setItemsFilter] = useState<'all' | 'pending' | 'approved' | 'swapped'>('all')
  const [swapsFilter, setSwapsFilter] = useState<'all' | 'sent' | 'received'>('all')

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      setLoading(true)

      // Fetch user profile
      const userResponse = await fetch('/api/auth/me')
      if (userResponse.ok) {
        const userData = await userResponse.json()
        setUser(userData.user)

        // Fetch user's items
        const itemsResponse = await fetch(`/api/items?userId=${userData.user._id}`)
        if (itemsResponse.ok) {
          const itemsData = await itemsResponse.json()
          setUserItems(itemsData.items)
        }

        // Fetch swap requests
        const swapsResponse = await fetch('/api/swaps?type=all')
        if (swapsResponse.ok) {
          const swapsData = await swapsResponse.json()
          setSwapRequests(swapsData.swaps)
        }

        // Fetch transaction history
        const transactionsResponse = await fetch('/api/transactions?limit=10')
        if (transactionsResponse.ok) {
          const transactionsData = await transactionsResponse.json()
          setTransactions(transactionsData.transactions)
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredItems = userItems.filter(item => {
    if (itemsFilter === 'all') return true
    return item.status === itemsFilter
  })

  const filteredSwaps = swapRequests.filter(swap => {
    if (swapsFilter === 'all') return true
    if (swapsFilter === 'sent') return swap.requesterId.toString() === user?._id?.toString()
    if (swapsFilter === 'received') return swap.targetUserId.toString() === user?._id?.toString()
    return true
  })

  const getItemCounts = () => {
    return {
      all: userItems.length,
      pending: userItems.filter(item => item.status === 'pending').length,
      approved: userItems.filter(item => item.status === 'approved').length,
      swapped: userItems.filter(item => item.status === 'swapped').length,
    }
  }

  const getSwapCounts = () => {
    return {
      all: swapRequests.length,
      sent: swapRequests.filter(swap => swap.requesterId.toString() === user?._id?.toString()).length,
      received: swapRequests.filter(swap => swap.targetUserId.toString() === user?._id?.toString()).length,
    }
  }

  const getSwapStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-yellow-500" />
      case 'accepted': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'rejected': return <XCircle className="w-4 h-4 text-red-500" />
      case 'cancelled': return <XCircle className="w-4 h-4 text-gray-500" />
      default: return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-1">
                <div className="h-64 bg-gray-200 rounded-lg"></div>
              </div>
              <div className="lg:col-span-2">
                <div className="h-64 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Profile not found</h1>
          <Link href="/login">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    )
  }

  const itemCounts = getItemCounts()
  const swapCounts = getSwapCounts()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage your ReWear profile, items, and swaps</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white p-1 rounded-lg shadow-sm">
          {[
            { key: 'overview', label: 'Overview', icon: TrendingUp },
            { key: 'items', label: 'My Items', icon: Package },
            { key: 'swaps', label: 'Swaps', icon: RefreshCw },
            { key: 'points', label: 'Points', icon: Coins },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex items-center gap-2 flex-1 px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Points Balance</p>
                      <p className="text-2xl font-bold text-primary">{user?.points || 0}</p>
                    </div>
                    <Coins className="w-8 h-8 text-primary" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Swaps</p>
                      <p className="text-2xl font-bold text-green-600">{user?.swapCount || 0}</p>
                    </div>
                    <RefreshCw className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Listed Items</p>
                      <p className="text-2xl font-bold text-blue-600">{itemCounts.approved}</p>
                    </div>
                    <Package className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending Swaps</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {swapRequests.filter(s => s.status === 'pending').length}
                      </p>
                    </div>
                    <Clock className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Profile Information */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <UserIcon className="w-5 h-5" />
                      <span>Profile Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-semibold mx-auto mb-4">
                        {user?.name.charAt(0).toUpperCase()}
                      </div>
                      <h2 className="text-xl font-semibold">{user?.name}</h2>
                      <Badge className="mt-2">
                        {capitalizeFirst(user?.role || 'user')}
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Mail className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">{user?.email}</span>
                      </div>

                      {user?.phone && (
                        <div className="flex items-center space-x-3">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <span className="text-sm">{user.phone}</span>
                        </div>
                      )}

                      {user?.address && (
                        <div className="flex items-start space-x-3">
                          <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                          <div className="text-sm">
                            <p>{user.address.street}</p>
                            <p>{user.address.city}, {user.address.state} {user.address.zipCode}</p>
                            <p>{user.address.country}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="pt-4 border-t">
                      <p className="text-sm text-gray-500">
                        Member since {user?.createdAt ? formatDate(user.createdAt) : 'N/A'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <History className="w-5 h-5" />
                      <span>Recent Activity</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {transactions.slice(0, 5).map((transaction, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            {transaction.type === 'earned' ? (
                              <ArrowUpRight className="w-5 h-5 text-green-500" />
                            ) : (
                              <ArrowDownLeft className="w-5 h-5 text-red-500" />
                            )}
                            <div>
                              <p className="text-sm font-medium">{transaction.description}</p>
                              <p className="text-xs text-gray-500">
                                {formatDate(transaction.createdAt)}
                              </p>
                            </div>
                          </div>
                          <div className={`text-sm font-semibold ${
                            transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.type === 'earned' ? '+' : ''}{transaction.amount} pts
                          </div>
                        </div>
                      ))}

                      {transactions.length === 0 && (
                        <div className="text-center py-8">
                          <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-500">No recent activity</p>
                        </div>
                      )}

                      {transactions.length > 5 && (
                        <div className="text-center pt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setActiveTab('points')}
                          >
                            View All Transactions
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}

        {/* Items Tab */}
        {activeTab === 'items' && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Package className="w-5 h-5" />
                  <span>My Items</span>
                </CardTitle>
                <Link href="/upload-item">
                  <Button size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Item
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {/* Items Filter Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
                {[
                  { key: 'all', label: 'All', count: itemCounts.all },
                  { key: 'pending', label: 'Pending', count: itemCounts.pending },
                  { key: 'approved', label: 'Available', count: itemCounts.approved },
                  { key: 'swapped', label: 'Swapped', count: itemCounts.swapped },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setItemsFilter(tab.key as any)}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      itemsFilter === tab.key
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>

              {/* Items Grid */}
              {filteredItems.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredItems.map((item) => (
                    <div key={item._id?.toString()} className="relative">
                      <ItemCard item={item} />
                      <div className="absolute top-2 left-2">
                        <Badge className={getStatusColor(item.status)}>
                          {capitalizeFirst(item.status)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    {itemsFilter === 'all'
                      ? "You haven't uploaded any items yet."
                      : `No ${itemsFilter} items found.`
                    }
                  </p>
                  <Link href="/upload-item">
                    <Button className="mt-4">
                      <Plus className="w-4 h-4 mr-2" />
                      Upload Your First Item
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Swaps Tab */}
        {activeTab === 'swaps' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <RefreshCw className="w-5 h-5" />
                <span>My Swaps</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Swaps Filter Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
                {[
                  { key: 'all', label: 'All', count: swapCounts.all },
                  { key: 'sent', label: 'Sent', count: swapCounts.sent },
                  { key: 'received', label: 'Received', count: swapCounts.received },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setSwapsFilter(tab.key as any)}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      swapsFilter === tab.key
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>

              {/* Swaps List */}
              {filteredSwaps.length > 0 ? (
                <div className="space-y-4">
                  {filteredSwaps.map((swap) => (
                    <div key={swap._id?.toString()} className="border rounded-lg p-4 bg-white">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            {getSwapStatusIcon(swap.status)}
                            <Badge className={getStatusColor(swap.status)}>
                              {capitalizeFirst(swap.status)}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              {swap.swapType === 'direct' ? 'Direct Swap' : 'Points Swap'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-900 mb-1">Target Item</p>
                              <p className="text-sm text-gray-600">{swap.targetItem.title}</p>
                              <p className="text-xs text-gray-500">by {swap.targetUser.name}</p>
                            </div>

                            {swap.swapType === 'direct' && swap.requesterItem && (
                              <div>
                                <p className="text-sm font-medium text-gray-900 mb-1">Your Item</p>
                                <p className="text-sm text-gray-600">{swap.requesterItem.title}</p>
                              </div>
                            )}

                            {swap.swapType === 'points' && (
                              <div>
                                <p className="text-sm font-medium text-gray-900 mb-1">Points Offered</p>
                                <p className="text-sm text-gray-600 flex items-center gap-1">
                                  <Coins className="w-4 h-4" />
                                  {swap.pointsOffered} pts
                                </p>
                              </div>
                            )}
                          </div>

                          {swap.message && (
                            <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                              <p className="font-medium text-gray-700">Message:</p>
                              <p className="text-gray-600">{swap.message}</p>
                            </div>
                          )}
                        </div>

                        <div className="text-right text-sm text-gray-500">
                          {formatDate(swap.createdAt)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <RefreshCw className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No swap requests found</p>
                  <Link href="/">
                    <Button className="mt-4">
                      Browse Items to Swap
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Points Tab */}
        {activeTab === 'points' && (
          <div className="space-y-6">
            {/* Points Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6 text-center">
                  <Coins className="w-8 h-8 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-600">Current Balance</p>
                  <p className="text-3xl font-bold text-primary">{user?.points || 0}</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <ArrowUpRight className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-600">Total Earned</p>
                  <p className="text-3xl font-bold text-green-600">{user?.totalPointsEarned || 0}</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <ArrowDownLeft className="w-8 h-8 text-red-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-600">Total Spent</p>
                  <p className="text-3xl font-bold text-red-600">{user?.totalPointsSpent || 0}</p>
                </CardContent>
              </Card>
            </div>

            {/* Transaction History */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <History className="w-5 h-5" />
                  <span>Transaction History</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {transactions.length > 0 ? (
                  <div className="space-y-3">
                    {transactions.map((transaction, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          {transaction.type === 'earned' ? (
                            <ArrowUpRight className="w-5 h-5 text-green-500" />
                          ) : (
                            <ArrowDownLeft className="w-5 h-5 text-red-500" />
                          )}
                          <div>
                            <p className="text-sm font-medium">{transaction.description}</p>
                            <p className="text-xs text-gray-500">
                              {formatDate(transaction.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className={`text-sm font-semibold ${
                          transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.type === 'earned' ? '+' : ''}{transaction.amount} pts
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No transactions yet</p>
                    <p className="text-sm text-gray-400 mt-2">
                      Start swapping to earn and spend points!
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
