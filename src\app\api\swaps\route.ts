import { NextRequest, NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'
import { getUserFromToken } from '@/lib/auth'
import { SwapRequest, SwapRequestWithDetails, CreateSwapRequestInput } from '@/models/Item'
import { Transaction, POINTS_CONFIG } from '@/models/Transaction'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'sent' | 'received' | 'all'
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit
    
    const db = await getDatabase()
    const swapsCollection = db.collection<SwapRequest>('swapRequests')
    
    // Build query based on user role and type
    const query: any = {}
    
    if (type === 'sent') {
      query.requesterId = new ObjectId(user.id)
    } else if (type === 'received') {
      query.targetUserId = new ObjectId(user.id)
    } else {
      // All swaps for this user
      query.$or = [
        { requesterId: new ObjectId(user.id) },
        { targetUserId: new ObjectId(user.id) }
      ]
    }
    
    if (status) {
      query.status = status
    }
    
    // Get swap requests with detailed information
    const swaps = await swapsCollection.aggregate<SwapRequestWithDetails>([
      { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'requesterId',
          foreignField: '_id',
          as: 'requester',
          pipeline: [{ $project: { password: 0 } }]
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'targetUserId',
          foreignField: '_id',
          as: 'targetUser',
          pipeline: [{ $project: { password: 0 } }]
        }
      },
      {
        $lookup: {
          from: 'items',
          localField: 'targetItemId',
          foreignField: '_id',
          as: 'targetItem'
        }
      },
      {
        $lookup: {
          from: 'items',
          localField: 'requesterItemId',
          foreignField: '_id',
          as: 'requesterItem'
        }
      },
      { $unwind: '$requester' },
      { $unwind: '$targetUser' },
      { $unwind: '$targetItem' },
      {
        $unwind: {
          path: '$requesterItem',
          preserveNullAndEmptyArrays: true
        }
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit }
    ]).toArray()
    
    const total = await swapsCollection.countDocuments(query)
    
    return NextResponse.json({
      swaps,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get swaps error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const body: CreateSwapRequestInput = await request.json()
    const { targetItemId, swapType, requesterItemId, pointsOffered, message } = body
    
    const db = await getDatabase()
    const itemsCollection = db.collection('items')
    const usersCollection = db.collection('users')
    const swapsCollection = db.collection<SwapRequest>('swapRequests')
    
    // Validate target item exists and is available
    const targetItem = await itemsCollection.findOne({
      _id: new ObjectId(targetItemId),
      status: 'approved',
      isActive: true
    })
    
    if (!targetItem) {
      return NextResponse.json(
        { error: 'Target item not found or not available' },
        { status: 404 }
      )
    }
    
    // Check if user is trying to swap with their own item
    if (targetItem.userId.toString() === user.id) {
      return NextResponse.json(
        { error: 'Cannot swap with your own item' },
        { status: 400 }
      )
    }
    
    // Validate swap type specific requirements
    if (swapType === 'direct') {
      if (!requesterItemId) {
        return NextResponse.json(
          { error: 'Requester item is required for direct swaps' },
          { status: 400 }
        )
      }
      
      const requesterItem = await itemsCollection.findOne({
        _id: new ObjectId(requesterItemId),
        userId: new ObjectId(user.id),
        status: 'approved',
        isActive: true
      })
      
      if (!requesterItem) {
        return NextResponse.json(
          { error: 'Your item not found or not available for swap' },
          { status: 404 }
        )
      }
    } else if (swapType === 'points') {
      if (!pointsOffered || pointsOffered < targetItem.pointsValue) {
        return NextResponse.json(
          { error: `Insufficient points offered. Required: ${targetItem.pointsValue}` },
          { status: 400 }
        )
      }
      
      // Check if user has enough points
      const requesterUser = await usersCollection.findOne({ _id: new ObjectId(user.id) })
      if (!requesterUser || requesterUser.points < pointsOffered) {
        return NextResponse.json(
          { error: 'Insufficient points balance' },
          { status: 400 }
        )
      }
    }
    
    // Check for existing pending swap request
    const existingSwap = await swapsCollection.findOne({
      requesterId: new ObjectId(user.id),
      targetItemId: new ObjectId(targetItemId),
      status: 'pending'
    })
    
    if (existingSwap) {
      return NextResponse.json(
        { error: 'You already have a pending swap request for this item' },
        { status: 400 }
      )
    }
    
    // Create swap request
    const newSwapRequest: SwapRequest = {
      requesterId: new ObjectId(user.id),
      requesterItemId: requesterItemId ? new ObjectId(requesterItemId) : undefined,
      targetItemId: new ObjectId(targetItemId),
      targetUserId: targetItem.userId,
      swapType,
      pointsOffered,
      message,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    const result = await swapsCollection.insertOne(newSwapRequest)
    
    return NextResponse.json(
      { 
        message: 'Swap request created successfully',
        swapId: result.insertedId
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Create swap request error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
