import { ObjectId } from 'mongodb'

export type ItemStatus = 'pending' | 'approved' | 'rejected' | 'swapped' | 'available'
export type ItemCondition = 'new' | 'like-new' | 'good' | 'fair' | 'poor'
export type ItemCategory = 'tops' | 'bottoms' | 'dresses' | 'outerwear' | 'shoes' | 'accessories' | 'other'
export type SwapType = 'direct' | 'points' // Direct swap or points-based redemption

export interface Item {
  _id?: ObjectId
  title: string
  description: string
  category: ItemCategory
  condition: ItemCondition
  size: string
  brand?: string
  originalPrice?: number
  pointsValue: number // Points required for redemption
  images: string[]
  userId: ObjectId
  status: ItemStatus
  rejectionReason?: string
  createdAt: Date
  updatedAt: Date
  swappedAt?: Date
  swappedWithUserId?: ObjectId
  swappedWithItemId?: ObjectId
  swapType?: SwapType
  tags?: string[]
  isActive: boolean
}

export interface CreateItemInput {
  title: string
  description: string
  category: ItemCategory
  condition: ItemCondition
  size: string
  brand?: string
  originalPrice?: number
  pointsValue: number
  images: string[]
  tags?: string[]
}

export interface UpdateItemInput {
  title?: string
  description?: string
  category?: ItemCategory
  condition?: ItemCondition
  size?: string
  brand?: string
  originalPrice?: number
  pointsValue?: number
  images?: string[]
  tags?: string[]
}

export interface ItemWithUser extends Item {
  user: {
    _id: ObjectId
    name: string
    email: string
    profileImage?: string
  }
}

// Swap Request Models
export type SwapRequestStatus = 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled'

export interface SwapRequest {
  _id?: ObjectId
  requesterId: ObjectId
  requesterItemId?: ObjectId // For direct swaps
  targetItemId: ObjectId
  targetUserId: ObjectId
  swapType: SwapType
  pointsOffered?: number // For points-based swaps
  message?: string
  status: SwapRequestStatus
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
}

export interface SwapRequestWithDetails extends SwapRequest {
  requester: {
    _id: ObjectId
    name: string
    email: string
    profileImage?: string
  }
  requesterItem?: ItemWithUser
  targetItem: ItemWithUser
  targetUser: {
    _id: ObjectId
    name: string
    email: string
    profileImage?: string
  }
}

export interface CreateSwapRequestInput {
  targetItemId: string
  swapType: SwapType
  requesterItemId?: string
  pointsOffered?: number
  message?: string
}
