import { ObjectId } from 'mongodb'

export type TransactionType = 'earned' | 'spent' | 'bonus' | 'penalty'
export type TransactionReason = 'item_upload' | 'swap_completed' | 'points_redemption' | 'admin_bonus' | 'admin_penalty' | 'referral_bonus'

export interface Transaction {
  _id?: ObjectId
  userId: ObjectId
  type: TransactionType
  amount: number
  reason: TransactionReason
  description: string
  relatedItemId?: ObjectId
  relatedSwapId?: ObjectId
  createdAt: Date
}

export interface CreateTransactionInput {
  userId: string
  type: TransactionType
  amount: number
  reason: TransactionReason
  description: string
  relatedItemId?: string
  relatedSwapId?: string
}

export interface TransactionWithDetails extends Transaction {
  user: {
    _id: ObjectId
    name: string
    email: string
  }
  relatedItem?: {
    _id: ObjectId
    title: string
    images: string[]
  }
}

// Points Configuration
export const POINTS_CONFIG = {
  ITEM_UPLOAD: 10, // Points earned for uploading an approved item
  SWAP_COMPLETED: 20, // Points earned for completing a swap
  REFERRAL_BONUS: 50, // Points earned for referring a new user
  WELCOME_BONUS: 100, // Points given to new users
} as const

export type PointsConfigKey = keyof typeof POINTS_CONFIG
