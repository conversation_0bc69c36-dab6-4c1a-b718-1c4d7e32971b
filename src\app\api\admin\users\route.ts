import { NextRequest, NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'
import { getUserFromToken } from '@/lib/auth'
import { User } from '@/models/User'

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit
    
    const db = await getDatabase()
    const usersCollection = db.collection<User>('users')
    
    // Get users (excluding passwords)
    const users = await usersCollection.find(
      {},
      { 
        projection: { password: 0 },
        sort: { createdAt: -1 },
        skip,
        limit
      }
    ).toArray()
    
    // Get total count
    const total = await usersCollection.countDocuments()
    
    // Calculate total points in circulation
    const pointsAggregation = await usersCollection.aggregate([
      {
        $group: {
          _id: null,
          totalPoints: { $sum: '$points' },
          totalEarned: { $sum: '$totalPointsEarned' },
          totalSpent: { $sum: '$totalPointsSpent' }
        }
      }
    ]).toArray()
    
    const pointsStats = pointsAggregation[0] || {
      totalPoints: 0,
      totalEarned: 0,
      totalSpent: 0
    }
    
    return NextResponse.json({
      users,
      totalPoints: pointsStats.totalPoints,
      totalEarned: pointsStats.totalEarned,
      totalSpent: pointsStats.totalSpent,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get users error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value
    const user = token ? getUserFromToken(token) : null
    
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    const { userId, action, points } = await request.json()
    
    const db = await getDatabase()
    const usersCollection = db.collection<User>('users')
    
    if (action === 'toggle_active') {
      const targetUser = await usersCollection.findOne({ _id: userId })
      if (!targetUser) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }
      
      await usersCollection.updateOne(
        { _id: userId },
        { $set: { isActive: !targetUser.isActive } }
      )
      
      return NextResponse.json({ 
        message: `User ${targetUser.isActive ? 'deactivated' : 'activated'} successfully` 
      })
    }
    
    if (action === 'adjust_points') {
      if (typeof points !== 'number') {
        return NextResponse.json(
          { error: 'Points must be a number' },
          { status: 400 }
        )
      }
      
      await usersCollection.updateOne(
        { _id: userId },
        { 
          $inc: { 
            points: points,
            ...(points > 0 ? { totalPointsEarned: points } : { totalPointsSpent: Math.abs(points) })
          }
        }
      )
      
      return NextResponse.json({ 
        message: `Points ${points > 0 ? 'added' : 'deducted'} successfully` 
      })
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Update user error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
