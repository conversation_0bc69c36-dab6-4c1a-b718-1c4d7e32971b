import { ObjectId } from 'mongodb'

export interface User {
  _id?: ObjectId
  email: string
  password: string
  name: string
  role: 'user' | 'admin'
  profileImage?: string
  phone?: string
  address?: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  points: number // Points balance for redemption system
  totalPointsEarned: number // Total points earned throughout lifetime
  totalPointsSpent: number // Total points spent throughout lifetime
  swapCount: number // Number of successful swaps completed
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

export interface CreateUserInput {
  email: string
  password: string
  name: string
  phone?: string
  address?: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
}

export interface LoginInput {
  email: string
  password: string
}

export interface UserSession {
  id: string
  email: string
  name: string
  role: 'user' | 'admin'
  points: number
  profileImage?: string
}
